// Removed DashboardSidebar and DashboardTopbar imports as they're now handled by DashboardLayout
import React, { useState, useEffect, useMemo } from "react";
import HighIcon from "../../../assets/High.svg";
import MediumIcon from "../../../assets/Medium.svg";
import NormalIcon from "../../../assets/Normal.svg";
import chartIcon from "../../../assets/chart-column-decreasing.svg";
import userGroupIcon from "../../../assets/users.svg";
import fileIcon from "../../../assets/file-text.svg";
import downloadIcon from "../../../assets/download.svg";

import { getAllProjects } from "../../../api/projectManagement";
import { getProjectTasks, transformTaskListData } from "../../../api/taskManagement";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import "../../../styles/Procedure.css"
import folderIcon from "../../../assets/folder.svg";
import { STATISTICS_ENDPOINTS } from "../../../api/endpoints";
import waitingIcon from "../../../assets/waiting.svg";
import triangleAlertIcon from "../../../assets/triangle-alert.svg";
import deploymentIcon from "../../../assets/deployment.svg";
import considerIcon from "../../../assets/consider.svg";
import completeIcon from "../../../assets/complete.svg";
import dropdownIcon from "../../../assets/icon-sidebar/dropdown.svg";
import searchIcon from "../../../assets/search.svg";
import todayIcon from "../../../assets/today.svg";

// Cache để tránh gọi API nhiều lần
let proceduresCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // Giảm xuống 10 giây để load nhanh hơn

// Statistical cache
let statsCache = null;
let statsCacheTimestamp = 0;

// Preload data for instant loading
let preloadPromise = null;

const ProcedureList = () => {
  const [procedures, setProcedures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [allProjects, setAllProjects] = useState([]);

  // Filter states
  const [selectedDepartment, setSelectedDepartment] = useState("Tất cả phòng ban");
  const [selectedProject, setSelectedProject] = useState("Tất cả dự án");
  const [selectedDateRange, setSelectedDateRange] = useState({ start: null, end: null });
  const [searchTerm, setSearchTerm] = useState("");

  // Dropdown states
  const [isDepartmentOpen, setIsDepartmentOpen] = useState(false);
  const [isProjectOpen, setIsProjectOpen] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Date picker states
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [selectingStart, setSelectingStart] = useState(true);
  const [isMonthSelectOpen, setIsMonthSelectOpen] = useState(false);
  const [isYearSelectOpen, setIsYearSelectOpen] = useState(false);



  // Statistical card states
  const [stats, setStats] = useState({
    totalProjects: 0,
    totalTasks: 0,
    completedTasks: 0,
    totalMembers: 0,
    avgProgress: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState(null);

  // Memoize procedures to prevent unnecessary re-renders
  const memoizedProcedures = useMemo(() => procedures, [procedures]);

  // Function to load statistical data
  const loadStats = async () => {
    const now = Date.now();
    if (statsCache && (now - statsCacheTimestamp) < CACHE_DURATION) {
      setStats(statsCache);
      setStatsLoading(false);
      return;
    }

    if (!statsCache) setStatsLoading(true);
    setStatsError(null);

    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json',
      };

      const [data1, data2] = await Promise.all([
        fetch(STATISTICS_ENDPOINTS.DEPARTMENT_STATS, { headers }).catch(() => ({ ok: false })),
        fetch(STATISTICS_ENDPOINTS.USER_STATS, { headers }).catch(() => ({ ok: false }))
      ]);

      let totalProjects = 0, totalTasks = 0, completedTasks = 0, totalMembers = 0, avgProgress = 0;

      if (data1.ok) {
        const result1 = await data1.json();
        totalProjects = result1.data?.totalProjects || 0;
        totalTasks = result1.data?.totalTasks || 0;
        completedTasks = result1.data?.completedTasks || 0;
      }

      if (data2.ok) {
        const result2 = await data2.json();
        totalMembers = result2.data?.totalMembers || 0;
      }

      if (totalTasks > 0 && completedTasks > 0) {
        avgProgress = Math.round((completedTasks / totalTasks) * 100);
      }

      const newStats = {
        totalProjects,
        totalTasks,
        completedTasks,
        totalMembers,
        avgProgress,
      };

      statsCache = newStats;
      statsCacheTimestamp = now;
      setStats(newStats);
    } catch (err) {
      console.warn('Error loading stats:', err);
      const fallbackStats = {
        totalProjects: 12,
        totalTasks: 50,
        completedTasks: 24,
        totalMembers: 24,
        avgProgress: 68,
      };
      setStats(fallbackStats);
      setStatsError('Không thể kết nối server, hiển thị dữ liệu mẫu');
    } finally {
      setStatsLoading(false);
    }
  };

  // Preload function
  const preloadData = async () => {
    if (preloadPromise) return preloadPromise;
    
    preloadPromise = (async () => {
      try {
        const projectsRes = await getAllProjects();
        const projects = projectsRes.data || [];

        if (projects.length === 0) return [];

        const projectDataPromises = projects.map(async (project) => {
          const projectId = project.id || project._id;
          try {
            const tasksRes = await getProjectTasks(projectId);
            const projectTasks = transformTaskListData(tasksRes.data || []);
            return projectTasks.map(task => ({
              ...task,
              projectName: project.name,
              projectId: projectId
            }));
          } catch (err) {
            return [];
          }
        });

        const allProjectData = await Promise.all(projectDataPromises);
        const allTasks = allProjectData.flat();
        
        proceduresCache = allTasks;
        cacheTimestamp = Date.now();
        
        return allTasks;
      } catch (err) {
        return [];
      }
    })();
    
    return preloadPromise;
  };

  useEffect(() => {
    async function fetchProcedures() {
      // Kiểm tra cache trước
      const now = Date.now();
      if (proceduresCache && (now - cacheTimestamp) < CACHE_DURATION) {
        setProcedures(proceduresCache);
        setLoading(false);
        return;
      }
      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);
      
      try {
        // Use preloaded data if available
        let allTasks = [];
        if (preloadPromise) {
          allTasks = await preloadPromise;
          setProcedures(allTasks);
        } else {
          // Fetch projects first
          const projectsRes = await getAllProjects();
          const projects = projectsRes.data || [];

          // Save projects list for dropdown
          setAllProjects(projects);

          if (projects.length === 0) {
            setProcedures([]);
            setLoading(false);
            return;
          }

          // Fetch all project data concurrently
          const projectDataPromises = projects.map(async (project) => {
            const projectId = project.id || project._id;
            
            try {
              // Fetch tasks for each project
              const tasksRes = await getProjectTasks(projectId);
              const projectTasks = transformTaskListData(tasksRes.data || []);

              return projectTasks.map(task => ({
                ...task,
                projectName: project.name,
                projectId: projectId
              }));
            } catch (err) {
              console.warn(`Error fetching data for project ${projectId}:`, err);
              return [];
            }
          });

          // Wait for all data to be fetched
          const allProjectData = await Promise.all(projectDataPromises);
          allTasks = allProjectData.flat();
          setProcedures(allTasks);
        }
        
        // Lưu vào cache
        proceduresCache = allTasks;
        cacheTimestamp = now;
        
      } catch (err) {
        console.warn('Error fetching procedures:', err);
        setProcedures([]);
        setError('Lỗi khi tải dữ liệu công việc');
      } finally {
        setLoading(false);
      }
    }
    
    fetchProcedures();
  }, [window.location.pathname]);

  // Start preloading when component mounts
  useEffect(() => {
    preloadData();
    loadStats();
  }, []);

  // Fetch projects list for dropdown
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const projectsRes = await getAllProjects();
        const projects = projectsRes.data || [];
        setAllProjects(projects);
      } catch (err) {
        console.warn('Error fetching projects for dropdown:', err);
      }
    };

    fetchProjects();
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.procedure-dropdown')) {
        setIsDepartmentOpen(false);
        setIsProjectOpen(false);
      }
      if (!event.target.closest('.date-picker-container') && !event.target.closest('.filter-date')) {
        setIsDatePickerOpen(false);
        setIsMonthSelectOpen(false);
        setIsYearSelectOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Date picker helper functions
  const monthNames = [
    "Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6",
    "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"
  ];

  const weekdays = ["Th2", "Th3", "Th4", "Th5", "Th6", "Th7", "CN"];

  const getDaysInMonth = (month, year) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (month, year) => {
    const firstDay = new Date(year, month, 1).getDay();
    return firstDay === 0 ? 6 : firstDay - 1; // Convert Sunday (0) to 6, Monday (1) to 0, etc.
  };

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth, currentYear);
    const firstDay = getFirstDayOfMonth(currentMonth, currentYear);
    const days = [];

    // Previous month days
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const daysInPrevMonth = getDaysInMonth(prevMonth, prevYear);

    for (let i = firstDay - 1; i >= 0; i--) {
      days.push({
        day: daysInPrevMonth - i,
        currentMonth: false,
        date: new Date(prevYear, prevMonth, daysInPrevMonth - i)
      });
    }

    // Current month days
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({
        day,
        currentMonth: true,
        date: new Date(currentYear, currentMonth, day)
      });
    }

    // Next month days
    const remainingDays = 42 - days.length; // 6 rows × 7 days
    const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;

    for (let day = 1; day <= remainingDays; day++) {
      days.push({
        day,
        currentMonth: false,
        date: new Date(nextYear, nextMonth, day)
      });
    }

    return days;
  };

  const navigateMonth = (direction) => {
    if (direction === -1) {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(currentYear - 1);
      } else {
        setCurrentMonth(currentMonth - 1);
      }
    } else {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
    }
  };

  const selectDate = (date) => {
    if (selectingStart || !selectedDateRange.start) {
      setSelectedDateRange({ start: date, end: null });
      setSelectingStart(false);
    } else {
      if (date < selectedDateRange.start) {
        setSelectedDateRange({ start: date, end: selectedDateRange.start });
      } else {
        setSelectedDateRange({ ...selectedDateRange, end: date });
      }
      setSelectingStart(true);
      setIsDatePickerOpen(false);
    }
  };

  const isDateInRange = (date) => {
    if (!selectedDateRange.start || !selectedDateRange.end) return false;
    return date >= selectedDateRange.start && date <= selectedDateRange.end;
  };

  const isDateSelected = (date) => {
    if (!selectedDateRange.start) return false;
    if (selectedDateRange.start.getTime() === date.getTime()) return true;
    if (selectedDateRange.end && selectedDateRange.end.getTime() === date.getTime()) return true;
    return false;
  };

  const formatDateRange = () => {
    if (!selectedDateRange.start) return "Chọn mốc thời gian";
    if (!selectedDateRange.end) {
      return `${selectedDateRange.start.getDate()}/${selectedDateRange.start.getMonth() + 1}/${selectedDateRange.start.getFullYear()} - ...`;
    }
    return `${selectedDateRange.start.getDate()}/${selectedDateRange.start.getMonth() + 1}/${selectedDateRange.start.getFullYear()} - ${selectedDateRange.end.getDate()}/${selectedDateRange.end.getMonth() + 1}/${selectedDateRange.end.getFullYear()}`;
  };

  // Month/Year dropdown functions
  const toggleMonthSelect = (e) => {
    e.stopPropagation();
    setIsMonthSelectOpen(!isMonthSelectOpen);
    setIsYearSelectOpen(false);
  };

  const toggleYearSelect = (e) => {
    e.stopPropagation();
    setIsYearSelectOpen(!isYearSelectOpen);
    setIsMonthSelectOpen(false);
  };

  const selectMonth = (monthIndex) => {
    setCurrentMonth(monthIndex);
    setIsMonthSelectOpen(false);
  };

  const selectYear = (year) => {
    setCurrentYear(year);
    setIsYearSelectOpen(false);
  };

  // Generate years range (from current year - 5 to current year + 5)
  const currentYearValue = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYearValue - 5 + i);



  // Department options
  const departmentOptions = [
    "Tất cả phòng ban",
    "IT",
    "Marketing",
    "Kế Toán",
    "Hành Chính Nhân Sự"
  ];

  // Project options (will be populated from API data)
  const projectOptions = [
    "Tất cả dự án",
    ...allProjects.map(project => project.name).filter(Boolean)
  ];
  const handleExport = () => {
    if (!memoizedProcedures || memoizedProcedures.length === 0) {
      alert('Không có dữ liệu để xuất!');
      return;
    }

    // Export to Excel
      const exportData = memoizedProcedures.map(({ projectId, projectName, id, name, startDate, dueDate, status, assignee, progress, priority, description }) => ({
        "Mã dự án": projectId,
        "Tên dự án": projectName,
        "Mã công việc": id,
        "Tên công việc": name,
        "Thời gian bắt đầu": startDate,
        "Thời gian kết thúc": dueDate,
        "Trạng thái":
          status === 'completed' ? 'Hoàn thành' :
          status === 'in-progress' ? 'Đang triển khai' :
          status === 'in_progress' ? 'Đang triển khai' :
          status === 'waiting' ? 'Đang chờ' :
          status === 'pending' ? 'Đang chờ' :
          status === 'overdue' ? 'Quá hạn' :
          status === 'review' ? 'Đang xem xét' :
          status === 'consider' ? 'Đang xem xét' : status,
        "Thành viên": assignee ? assignee.map(u => u.name).join(", ") : "",
        "Tiến độ": progress || (status === 'completed' ? 100 : status === 'in-progress' ? 50 : 0),
        "Độ ưu tiên": priority,
        "Mô tả": description
      }));
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "QuyTrinhCongViec");
      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      saveAs(new Blob([wbout], { type: "application/octet-stream" }), "quy_trinh_cong_viec.xlsx");
  };

  // Helper format date
  const formatDate = (dateStr) => {
    if (!dateStr) return 'N/A';
    
    // If it's already in DD/MM/YYYY format, return as is
    if (typeof dateStr === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
      return dateStr;
    }
    
    const d = new Date(dateStr);
    if (isNaN(d)) return dateStr;
    
    // Format to DD/MM/YYYY
    const day = d.getDate();
    const month = d.getMonth() + 1;
    const year = d.getFullYear();
    
    return `${day}/${month}/${year}`;
  };

  // Helper function to get user avatar (real avatar from user account)
  const getAvatarUrl = (user) => {
    const defaultAvatar = 'https://randomuser.me/api/portraits/men/1.jpg';

    if (!user) return defaultAvatar;

    // Kiểm tra avatar trực tiếp từ user
    if (user.avatar && user.avatar.trim() !== '' && !user.avatar.includes('ui-avatars.com/api/?name=?')) {
      return user.avatar;
    }

    // Kiểm tra avatar trong user.user (nested structure)
    if (user.user && user.user.avatar && user.user.avatar.trim() !== '' && !user.user.avatar.includes('ui-avatars.com/api/?name=?')) {
      return user.user.avatar;
    }

    // Fallback: tạo avatar từ tên người dùng bằng ui-avatars.com
    const name = user.name || (user.user && user.user.name) || user.fullName || "Thành viên";
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=0074b7&color=ffffff&size=128`;
  };

  // Helper format mã dự án/công việc
  const formatCode = (id, type = 'DA') => {
    if (!id) return '';
    const str = String(id);
    return type + str.slice(-6);
  };

  // Helper to get status icon
  const getStatusIcon = (status) => {
    const statusLower = String(status || '').toLowerCase();
    switch (statusLower) {
      case "in_progress":
      case "in-progress":
      case "đang triển khai":
        return deploymentIcon;
      case "completed":
      case "hoàn thành":
        return completeIcon;
      case "pending":
      case "waiting":
      case "đang chờ":
        return waitingIcon;
      case "overdue":
      case "quá hạn":
        return triangleAlertIcon;
      case "review":
      case "consider":
      case "đang xem xét":
        return considerIcon;
      default:
        return waitingIcon;
    }
  };

  // Hàm lấy icon mức độ ưu tiên
  const getPriorityIcon = (priority) => {
    const p = String(priority).toLowerCase();
    switch (p) {
      case "high":
      case "cao":
        return HighIcon; // đỏ
      case "critical":
      case "urgent":
      case "khẩn cấp":
      case "khan cap":
        return HighIcon; // đỏ
      case "medium":
      case "trung bình":
      case "trung binh":
        return MediumIcon; // cam
      case "low":
      case "thấp":
      case "thap":
        return NormalIcon; // xanh lá
      default:
        return NormalIcon;
    }
  };



  return (
    <>
      <div className="statistical-list-container">
        {/* Statistical Cards Section */}
        <div className="statistical-cards-wrapper">
          <div className="statistical-list-title">
            <img
              src={folderIcon}
              alt="folder"
              style={{ width: 22, height: 22, marginRight: 8, marginBottom: 3, verticalAlign: 'middle' }}
            />
            Báo cáo công việc
          </div>

          {statsLoading ? (
            <div className="statistical-cards">
              {/* Skeleton cho 4 cards */}
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="stat-card" style={{ opacity: 0.7 }}>
                  <div className="stat-title stat-title-flex">
                    <div style={{
                      width: '80px',
                      height: '16px',
                      background: '#f0f0f0',
                      borderRadius: '4px',
                      animation: 'pulse 1.5s ease-in-out infinite'
                    }}></div>
                    <div style={{
                      width: 20,
                      height: 20,
                      background: '#f0f0f0',
                      borderRadius: '4px',
                      animation: 'pulse 1.5s ease-in-out infinite'
                    }}></div>
                  </div>
                  <div style={{
                    width: '60px',
                    height: '32px',
                    background: '#f0f0f0',
                    borderRadius: '4px',
                    marginBottom: '4px',
                    animation: 'pulse 1.5s ease-in-out infinite'
                  }}></div>
                  <div style={{
                    width: '100px',
                    height: '14px',
                    background: '#f0f0f0',
                    borderRadius: '4px',
                    animation: 'pulse 1.5s ease-in-out infinite'
                  }}></div>
                </div>
              ))}
            </div>
          ) : statsError ? (
            <div style={{ padding: 20, textAlign: 'center', color: 'red' }}>Lỗi: {statsError}</div>
          ) : (
            <div className="statistical-cards">
              <div className="stat-card">
                <div className="stat-title stat-title-flex">
                  Tổng dự án
                  <img
                    src={fileIcon}
                    alt="Tổng dự án"
                    style={{ width: 20, marginBottom: -4 }}
                  />
                </div>
                <div className="stat-value">{stats.totalProjects}</div>
                <div className="stat-desc">+2 tháng trước</div>
              </div>
              <div className="stat-card">
                <div className="stat-title stat-title-flex">
                  Công việc
                  <img
                    src={chartIcon}
                    alt="Công việc"
                    style={{ width: 20, marginBottom: -4 }}
                  />
                </div>
                <div className="stat-value">{stats.totalTasks}</div>
                <div className="stat-desc">{stats.completedTasks} Hoàn thành</div>
              </div>
              <div className="stat-card">
                <div className="stat-title stat-title-flex">
                  Thành viên
                  <img
                    src={userGroupIcon}
                    alt="Thành viên"
                    style={{ width: 20, marginBottom: -4 }}
                  />
                </div>
                <div className="stat-value">{stats.totalMembers}</div>
                <div className="stat-desc">+1 tháng trước</div>
              </div>
              <div className="stat-card">
                <div className="stat-title stat-title-flex">
                  Tiến độ TB
                  <img
                    src={fileIcon}
                    alt="Tiến độ TB"
                    style={{ width: 20, marginBottom: -4 }}
                  />
                </div>
                <div className="stat-value">{stats.avgProgress}%</div>
                <div className="stat-desc">+6 tuần trước</div>
              </div>
            </div>
          )}

          <div className="statistical-filters-row">
            <div className="filter-controls">
              <div className="filter-group">
                <div className="procedure-dropdown">
                  <button
                    className="procedure-dropdown-btn"
                    onClick={() => setIsDepartmentOpen(!isDepartmentOpen)}
                  >
                    <span>{selectedDepartment}</span>
                    <img src={dropdownIcon} alt="dropdown" className="select-icon" />
                  </button>
                  {isDepartmentOpen && (
                    <div className="procedure-dropdown-menu">
                      {departmentOptions.map((dept) => (
                        <div
                          key={dept}
                          className="procedure-dropdown-item"
                          onClick={() => {
                            setSelectedDepartment(dept);
                            setIsDepartmentOpen(false);
                          }}
                        >
                          {dept}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="filter-group">
                <div className="procedure-dropdown">
                  <button
                    className="procedure-dropdown-btn"
                    onClick={() => setIsProjectOpen(!isProjectOpen)}
                  >
                    <span>{selectedProject}</span>
                    <img src={dropdownIcon} alt="dropdown" className="select-icon" />
                  </button>
                  {isProjectOpen && (
                    <div className="procedure-dropdown-menu">
                      {projectOptions.map((project) => (
                        <div
                          key={project}
                          className="procedure-dropdown-item"
                          onClick={() => {
                            setSelectedProject(project);
                            setIsProjectOpen(false);
                          }}
                        >
                          {project}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="filter-group">
                <div className="date-picker-wrapper">
                  <button
                    className="filter-date"
                    onClick={() => setIsDatePickerOpen(!isDatePickerOpen)}
                  >
                    <span>{formatDateRange()}</span>
                    <img src={todayIcon} alt="calendar" className="date-icon" />
                  </button>

                  {isDatePickerOpen && (
                    <div className="date-picker-container">
                      <div className="date-picker-header">
                        <button onClick={() => navigateMonth(-1)} className="month-nav-btn">
                          ‹
                        </button>
                        <div className="date-picker-selectors">
                          <button
                            className="month-selector-btn"
                            onClick={toggleMonthSelect}
                          >
                            {monthNames[currentMonth]}
                            <span className="dropdown-arrow">▼</span>
                          </button>
                          <button
                            className="year-selector-btn"
                            onClick={toggleYearSelect}
                          >
                            {currentYear}
                            <span className="dropdown-arrow">▼</span>
                          </button>

                          {isMonthSelectOpen && (
                            <div className="month-select-dropdown">
                              <div className="month-select-list">
                                {monthNames.map((month, index) => (
                                  <div
                                    key={index}
                                    className={`month-option ${index === currentMonth ? 'selected' : ''}`}
                                    onClick={() => selectMonth(index)}
                                  >
                                    {month}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {isYearSelectOpen && (
                            <div className="year-select-dropdown">
                              <div className="year-select-list">
                                {years.map(year => (
                                  <div
                                    key={year}
                                    className={`year-option ${year === currentYear ? 'selected' : ''}`}
                                    onClick={() => selectYear(year)}
                                  >
                                    {year}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                        <button onClick={() => navigateMonth(1)} className="month-nav-btn">
                          ›
                        </button>
                      </div>

                      <div className="date-picker-weekdays">
                        {weekdays.map(day => (
                          <div key={day} className="date-picker-weekday">{day}</div>
                        ))}
                      </div>

                      <div className="date-picker-days">
                        {generateCalendarDays().map((dateObj, index) => (
                          <div
                            key={index}
                            className={`date-picker-day ${!dateObj.currentMonth ? 'other-month' : ''}
                                        ${isDateSelected(dateObj.date) ? 'selected' : ''}
                                        ${isDateInRange(dateObj.date) ? 'in-range' : ''}`}
                            onClick={() => dateObj.currentMonth && selectDate(dateObj.date)}
                          >
                            {dateObj.day}
                          </div>
                        ))}
                      </div>

                      <div className="date-picker-actions">
                        <button
                          onClick={() => {
                            setSelectedDateRange({ start: null, end: null });
                            setSelectingStart(true);
                          }}
                          className="clear-btn"
                        >
                          Xóa
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="filter-group">
                <input
                  type="text"
                  className="filter-search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Tìm kiếm nhân sự..."
                />
                <img src={searchIcon} alt="search" className="search-icon" />
              </div>
            </div>

            <div className="statistical-actions">
              <button className="btn-export" type="button" onClick={handleExport}>
                <img
                  src={downloadIcon}
                  alt="Xuất Excel"
                  style={{ width: 18, marginRight: 6 }}
                />
                Xuất Excel
              </button>
            </div>
          </div>

          {/* CSS Animation */}
          <style>{`
            @keyframes pulse {
              0% { opacity: 1; }
              50% { opacity: 0.5; }
              100% { opacity: 1; }
            }
          `}</style>
        </div>
        <div style={{ marginTop: 24 }}>
          {loading && memoizedProcedures.length === 0 ? (
            <div>
              {/* Loading skeleton - only show when no data available */}
              {[1, 2, 3, 4, 5].map((idx) => (
                <div key={idx} className="procedure-card skeleton">
                  <div className="procedure-card-row" style={{gap: 32}}>
                    <div className="procedure-col procedure-col-left">
                      <div className="skeleton-title"></div>
                      <div className="skeleton-text"></div>
                      <div className="skeleton-text"></div>
                      <div className="skeleton-avatars">
                        <div className="skeleton-avatar"></div>
                        <div className="skeleton-avatar"></div>
                        <div className="skeleton-avatar"></div>
                      </div>
                      <div className="skeleton-text"></div>
                    </div>
                    <div className="procedure-col procedure-col-center">
                      <div className="skeleton-title"></div>
                      <div className="skeleton-text"></div>
                      <div className="skeleton-text"></div>
                    </div>
                    <div className="procedure-col procedure-col-right">
                      <div className="skeleton-text"></div>
                      <div className="skeleton-progress"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="error-container">
              <div style={{ color: 'red', textAlign: 'center', padding: '20px' }}>
                <div>❌ {error}</div>
                <button
                  onClick={() => window.location.reload()}
                  style={{
                    marginTop: '10px',
                    padding: '8px 16px',
                    background: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Thử lại
                </button>
              </div>
            </div>
          ) : !loading && memoizedProcedures.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
              <div>📋 Không có dữ liệu công việc.</div>
            </div>
          ) : (
            <div>
              {/* Show real data immediately */}
              {memoizedProcedures.map((item, idx) => (
                <div
                  key={item.id || item._id || idx}
                  className="procedure-card"
                >
                  <div className="procedure-card-row" style={{gap: 32}}>
                    {/* Cột trái: Thông tin công việc, người thực hiện, mô tả */}
                    <div className="procedure-col procedure-col-left">
                      <div className="procedure-title">{(item.name || item.title || 'Thiết kế giao diện người dùng').replace(/^TASK-[A-Z0-9]+ /, '')}</div>
                      <div className="procedure-id">Mã công việc: {formatCode(item.id, 'TSK-')}</div>

                      <div className="procedure-label" style={{marginTop: 16}}>Người thực hiện</div>
                      <div className="procedure-value" style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
                        {item.assignee && item.assignee.length > 0 ? (
                          item.assignee.map((u, idx) => (
                            <div key={(u.id || u._id || u.name || idx) + idx} className="avatar-container" style={{ position: 'relative' }}>
                              <img
                                src={getAvatarUrl(u)}
                                alt={u.name || (u.user && u.user.name) || "Thành viên"}
                                style={{
                                  width: '24px',
                                  height: '24px',
                                  borderRadius: '50%',
                                  cursor: 'pointer',
                                  objectFit: 'cover'
                                }}
                                onError={(e) => {
                                  e.target.src = 'https://randomuser.me/api/portraits/men/1.jpg';
                                }}
                                onMouseEnter={(e) => {
                                  const tooltip = document.createElement('div');
                                  tooltip.className = 'avatar-tooltip';
                                  tooltip.textContent = u.name || (u.user && u.user.name) || "Thành viên";
                                  tooltip.style.cssText = `
                                    position: fixed;
                                    top: ${e.target.getBoundingClientRect().top - 35}px;
                                    left: ${e.target.getBoundingClientRect().left + e.target.offsetWidth/2}px;
                                    transform: translateX(-50%);
                                    background: #333;
                                    color: white;
                                    padding: 6px 10px;
                                    border-radius: 6px;
                                    font-size: 12px;
                                    white-space: nowrap;
                                    z-index: 9999;
                                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                                    pointer-events: none;
                                  `;
                                  document.body.appendChild(tooltip);

                                  // Animation
                                  tooltip.style.opacity = '0';
                                  tooltip.style.transform = 'translateX(-50%) translateY(5px)';
                                  setTimeout(() => {
                                    tooltip.style.transition = 'all 0.2s ease-out';
                                    tooltip.style.opacity = '1';
                                    tooltip.style.transform = 'translateX(-50%) translateY(0)';
                                  }, 10);
                                }}
                                onMouseLeave={() => {
                                  const tooltips = document.querySelectorAll('.avatar-tooltip');
                                  tooltips.forEach(tooltip => tooltip.remove());
                                }}
                              />
                            </div>
                          ))
                        ) : (
                          <span style={{ color: '#888', fontStyle: 'italic' }}>Chưa phân công</span>
                        )}
                      </div>

                      <div className="procedure-label" style={{marginTop: 16}}>Mô tả công việc</div>
                      <div className="procedure-desc">
                        {item.description && item.description.length > 80
                          ? item.description.slice(0, 80) + '...'
                          : item.description || 'Không có mô tả'}
                      </div>
                    </div>

                    {/* Cột giữa: Thông tin dự án, thời gian thực hiện */}
                    <div className="procedure-col procedure-col-center">
                      <div className="procedure-title">{item.projectName || 'Hệ thống quản lí bán hàng'}</div>
                      <div className="procedure-id">Mã dự án: {item.projectCode || item.project?.projectCode || 'PRJ-N/A'}</div>

                      <div className="procedure-label" style={{marginTop: 16}}>Thời gian thực hiện</div>
                      <div className="procedure-value">{formatDate(item.startDate)} - {formatDate(item.dueDate)}</div>
                    </div>

                    {/* Cột phải: Priority, Status, Progress */}
                    <div className="procedure-col procedure-col-right">
                      <div className="procedure-priority">
                        <img src={getPriorityIcon(item.priority)} alt="priority" className="procedure-priority-icon" />
                        <span className={`procedure-priority-text ${item.priority || 'normal'}`}>{
                          (item.priority || 'normal') === 'high'
                            ? 'Cao'
                            : (item.priority || 'normal') === 'medium'
                            ? 'Trung bình'
                            : 'Thấp'
                        }</span>
                      </div>

                      <div className="procedure-done" style={{marginTop: 40}}>
                        <img src={getStatusIcon(item.status)} alt="status" className="procedure-done-icon" />
                        <span className={`procedure-done-text ${item.status || 'waiting'}`}>
                          {(() => {
                            const status = String(item.status || '').toLowerCase();
                            switch (status) {
                              case 'completed':
                              case 'hoàn thành':
                                return 'Hoàn thành';
                              case 'in-progress':
                              case 'in_progress':
                              case 'đang triển khai':
                                return 'Đang triển khai';
                              case 'waiting':
                              case 'pending':
                              case 'đang chờ':
                                return 'Đang chờ';
                              case 'overdue':
                              case 'quá hạn':
                                return 'Quá hạn';
                              case 'review':
                              case 'consider':
                              case 'đang xem xét':
                                return 'Đang xem xét';
                              default:
                                return 'Đang chờ';
                            }
                          })()}
                        </span>
                      </div>

                      <div className="procedure-progress-section" style={{marginTop: 16}}>
                        <div className="procedure-progress-label">Tiến độ</div>
                        <div className="procedure-progress">
                          <div className="procedure-progress-bar">
                            <div
                              className="procedure-progress-fill"
                              style={{ width: `${item.progress || 0}%` }}
                            ></div>
                          </div>
                          <span className="procedure-progress-text">{item.progress || 0}%</span>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              ))}

            </div>
          )}
        </div>
        {/* Add bottom spacing for better visual appearance */}
        <div style={{ height: '40px' }}></div>
      </div>


    </>
  );
};

export default ProcedureList;
