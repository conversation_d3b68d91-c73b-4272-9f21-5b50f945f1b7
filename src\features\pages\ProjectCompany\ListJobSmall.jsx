import React, { useState, useEffect } from 'react';
import startIcon from '../../../assets/start.svg';
import doneIcon from '../../../assets/done.svg';
import undoIcon from '../../../assets/undo.svg';
import userIcon from '../../../assets/user.svg';
import alarmClockPlusIcon from '../../../assets/alarm-clock-plus.svg';
import deploymentIcon from '../../../assets/deployment.svg';
import completeIcon from '../../../assets/complete.svg';
import waitingIcon from '../../../assets/waiting.svg';
import overdueIcon from '../../../assets/triangle-alert.svg';
import considerIcon from '../../../assets/consider.svg';
  // Lấy icon trạng thái giống Big
  const getStatusIcon = (status) => {
    switch (status) {
      case '<PERSON>ang thực hiện':
        return deploymentIcon;
      case '<PERSON><PERSON><PERSON> thành':
        return completeIcon;
      case '<PERSON><PERSON> chờ':
        return waitingIcon;
      case 'Quá hạn':
        return overdueIcon;
      case 'Xem xét':
        return considerIcon;
      default:
        return waitingIcon;
    }
  };
import '../../../styles/ListJobSmall.css';
import { 
  getAllSmallTasks, 
  updateSmallTaskStatus, 
  updateSmallTaskTime,
  deleteSmallTask 
} from '../../../storage/smallTasksData';

const ListJobSmall = ({ task, onTaskUpdate, onTaskDelete }) => {
  const [currentTask, setCurrentTask] = useState(task);
  // Lọc các nhiệm vụ phụ thuộc hợp lệ (không rỗng, không null)
  const validDependencies = Array.isArray(currentTask.dependencies)
    ? currentTask.dependencies.filter(dep => dep && dep.trim() !== "")
    : [];

  useEffect(() => {
    setCurrentTask(task);
  }, [task]);

  const handleStartTask = () => {
    const updatedTask = updateSmallTaskStatus(currentTask.id, 'Đang thực hiện');
    setCurrentTask(updatedTask.find(t => t.id === currentTask.id));
    if (onTaskUpdate) {
      onTaskUpdate(updatedTask);
    }
  };

  const handleStatusChange = (newStatus) => {
    const updatedTask = updateSmallTaskStatus(currentTask.id, newStatus);
    setCurrentTask(updatedTask.find(t => t.id === currentTask.id));
    if (onTaskUpdate) {
      onTaskUpdate(updatedTask);
    }
  };

  const handleDelete = () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa nhiệm vụ này?')) {
      const updatedTasks = deleteSmallTask(currentTask.id);
      if (onTaskDelete) {
        onTaskDelete(updatedTasks);
      }
    }
  };

  return (
    <div className="task-card">
      {/* Header Section */}
      <div className="task-header">
        <div className="task-title-section">
          <span className="task-category">{currentTask.category}</span>
          <span className="task-status">
            <img src={getStatusIcon(currentTask.status)} alt="status" style={{width: '18px', height: '18px', verticalAlign: 'middle'}} />
            {currentTask.status}
          </span>
          {validDependencies.length > 0 && (
            <span className="dependency-count-tag" style={{marginLeft: '12px', padding: '2px 12px', border: '1px solid #ccc', borderRadius: '16px', fontSize: '14px', color: '#666', background: '#fafafa'}}>
              {`Phụ thuộc ${validDependencies.length} nhiệm vụ`}
            </span>
          )}
        </div>
        {currentTask.status === 'Đang chờ' && (
          // Nếu có phụ thuộc và chưa hoàn thành thì nút 'Bắt đầu' disabled, hiển thị 'Đang chờ'
          validDependencies.length > 0 ? (
            <button className="waiting-button" disabled style={{background: '#bdbdbd', color: '#fff', borderRadius: '8px', padding: '6px 16px', border: 'none', cursor: 'not-allowed', display: 'flex', alignItems: 'center', gap: '8px'}}>
              Đang chờ
            </button>
          ) : (
            <button className="start-button" onClick={handleStartTask}>
              <img src={startIcon} alt="start" className="start-svg-icon" />
              Bắt đầu
            </button>
          )
        )}
        {currentTask.status === 'Đang thực hiện' && (
          <button className="complete-button" onClick={() => {
            const updatedTask = updateSmallTaskStatus(currentTask.id, 'Hoàn thành');
            setCurrentTask(updatedTask.find(t => t.id === currentTask.id));
            if (onTaskUpdate) {
              onTaskUpdate(updatedTask);
            }
          }} style={{marginLeft: '8px', background: '#4caf50', color: '#fff', borderRadius: '8px', padding: '6px 16px', border: 'none', cursor: 'pointer', display: 'flex', alignItems: 'center', gap: '8px'}}>
            <img src={doneIcon} alt="done" style={{width: '20px', height: '20px'}} />
            Hoàn thành
          </button>
        )}
        {currentTask.status === 'Hoàn thành' && (
          <button className="undo-button" onClick={() => {
            const updatedTask = updateSmallTaskStatus(currentTask.id, 'Đang thực hiện');
            setCurrentTask(updatedTask.find(t => t.id === currentTask.id));
            if (onTaskUpdate) {
              onTaskUpdate(updatedTask);
            }
          }} style={{marginLeft: '8px', background: '#2196f3', color: '#fff', borderRadius: '8px', padding: '6px 16px', border: 'none', cursor: 'pointer', display: 'flex', alignItems: 'center', gap: '8px'}}>
            <img src={undoIcon} alt="undo" style={{width: '20px', height: '20px'}} />
            Hoàn tác công việc
          </button>
        )}
      </div>

      {/* Task Description */}
      <div className="task-description">
        {currentTask.description}
      </div>

      {/* Middle Section - Assignee and Time */}
      <div className="task-details">
        <div className="assignee-section">
          <div className="detail-label">
            <img src={userIcon} alt="user" className="user-svg-icon" />
            Người thực hiện
          </div>
          <div className="assignee-info">
            <div className="assignee-avatar">
              <span className="avatar-text">{currentTask.assignee.name.charAt(0)}</span>
            </div>
            <span className="assignee-name">{currentTask.assignee.name}</span>
          </div>
        </div>

        <div className="time-section">
          <div className="time-labels-row">
            <div className="time-label-item">
              <div className="detail-label">
                <svg className="clock-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                  <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
                </svg>
                Thời gian dự kiến
              </div>
              <span className="time-value">{currentTask.estimatedTime}</span>
            </div>

            <div className="time-label-item">
              <div className="detail-label">
                <img src={alarmClockPlusIcon} alt="alarm-clock-plus" className="alarm-clock-plus-svg-icon" />
                Thời gian thực tế
              </div>
              <span className="time-value">{currentTask.actualTime}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section - Dependencies */}
      <div className="task-dependencies">
        <div className="dependency-title">Phụ thuộc nhiệm vụ</div>
        <div className="dependency-content">
          <div style={{marginBottom: '8px'}}>
            <span className="dependency-label">Phụ thuộc vào</span>
            {validDependencies.length > 0
              ? validDependencies.map((dep, idx) => (
                  <span key={idx} className="dependency-tag" style={{marginLeft: '8px', background: '#f3f3f3', borderRadius: '12px', padding: '2px 10px', color: '#333'}}>{dep}</span>
                ))
              : <span className="dependency-tag" style={{marginLeft: '8px', background: '#f3f3f3', borderRadius: '12px', padding: '2px 10px', color: '#333'}}>Không có</span>
            }
          </div>
          <div>
            <span className="dependency-label">Khối</span>
            <span className="dependency-tag" style={{marginLeft: '8px', background: '#f3f3f3', borderRadius: '12px', padding: '2px 10px', color: '#333'}}>
              {currentTask.dependency && currentTask.dependency.trim() !== '' ? currentTask.dependency : 'Không có'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListJobSmall;