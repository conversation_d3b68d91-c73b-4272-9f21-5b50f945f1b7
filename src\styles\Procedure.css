@import url('../index.css');
/* ========== STATISTICAL CARDS STYLES ========== */
.statistical-cards-wrapper {
  width: 100%;
}
.statistical-list-title{
  font-size: 22px;
  font-weight: 600;
  color: #5D5D5D;
  padding: 0px 0px 20px 0px;
}
.statistical-cards {
  display: flex;
  gap: 24px;
}
.stat-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 24px 32px 16px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 180px;
}
.stat-title {
  font-size: 15px;
  color: #888;
  margin-bottom: 8px;
}
.stat-title.stat-title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}
.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #222;
}
.stat-desc {
  font-size: 13px;
  color: #bdbdbd;
  margin-top: 4px;
}
.statistical-filters-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 24px 0;
  gap: 16px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.filter-group {
  position: relative;
  display: flex;
  align-items: center;
}

.filter-select {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 10px 32px 10px 12px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #5d5d5d;
  cursor: pointer;
  outline: none;
  appearance: none;
  min-width: 140px;
  transition: border-color 0.2s;
}

.filter-select:hover,
.filter-select:focus {
  border-color: #007bff;
}

.select-icon {
  position: absolute;
  right: 10px;
  width: 18px;
  height: 18px;
  pointer-events: none;
}

.filter-date {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 10px 32px 10px 12px;
  font-weight: 500;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  color: #5d5d5d;
  cursor: pointer;
  outline: none;
  min-width: 160px;
  transition: border-color 0.2s;
}

.filter-date:hover,
.filter-date:focus {
  border-color: #bfc5ce;
}

.filter-date::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.date-icon {
  position: absolute;
  right: 10px;
  width: 24px;
  height: 24px;
  opacity: 0.7;
  pointer-events: none;
}

/* Date Range Picker Styling */
.date-picker-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.filter-date {
  width: 100%;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 10px 32px 10px 12px;
  font-size: 14px;
  font-weight: 500;
  color: #5d5d5d;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  cursor: pointer;
  outline: none;
  min-width: 250px;
  transition: border-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.filter-date:hover,
.filter-date:focus {
  border-color: #007bff;
}

.date-picker-container {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin-top: 8px;
  padding: 16px;
  width: 360px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.date-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.month-nav-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.month-nav-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

.date-picker-selectors {
  display: flex;
  position: relative;
}

.month-selector-btn,
.year-selector-btn {
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 600;
  color: #5d5d5d;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.month-selector-btn:hover,
.year-selector-btn:hover {
  background-color: #f0f0f0;
}

.month-selector-btn {
  margin-right: 4px;
}

.dropdown-arrow {
  font-size: 10px;
  margin-left: 4px;
  color: #666;
}

.year-select-dropdown,
.month-select-dropdown {
  position: absolute;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 5;
  width: 120px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
}

.year-select-dropdown {
  right: 0;
  top: 100%;
  margin-top: 4px;
}

.month-select-dropdown {
  left: 0;
  top: 100%;
  margin-top: 4px;
}

.year-select-list,
.month-select-list {
  padding: 4px 0;
}

.year-option,
.month-option {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.year-option:hover,
.month-option:hover {
  background-color: #f0f0f0;
}

.year-option.selected,
.month-option.selected {
  background-color: #007bff;
  color: white;
  font-weight: 600;
}

/* Scrollbar styles for dropdowns */
.year-select-dropdown::-webkit-scrollbar,
.month-select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.year-select-dropdown::-webkit-scrollbar-track,
.month-select-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.year-select-dropdown::-webkit-scrollbar-thumb,
.month-select-dropdown::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.year-select-dropdown::-webkit-scrollbar-thumb:hover,
.month-select-dropdown::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

.date-picker-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.date-picker-weekday {
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #5d5d5d;
  padding: 8px 4px;
}

.date-picker-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.date-picker-day {
  text-align: center;
  padding: 8px 4px;
  font-size: 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s;
  color: #333;
}

.date-picker-day:hover {
  background-color: #f0f7ff;
}

.date-picker-day.other-month {
  color: #ccc;
}

.date-picker-day.selected {
  background-color: #007bff;
  color: white;
  font-weight: 600;
}

.date-picker-day.in-range {
  background-color: #e3f2fd;
  color: #1976d2;
}

.date-picker-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.clear-btn {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-btn:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.filter-search {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 10px 32px 10px 12px;
  font-size: 14px;
  color: #5d5d5d;
  outline: none;
  min-width: 200px;
  transition: border-color 0.2s;
}

.filter-search:hover,
.filter-search:focus {
  border-color: #007bff;
}

.filter-search::placeholder {
  color: #999;
}

.search-icon {
  position: absolute;
  right: 10px;
  width: 16px;
  height: 16px;
  pointer-events: none;
}

/* Custom dropdown styling */
.procedure-dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}

.procedure-dropdown-btn {
  width: 250px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 10px 32px 10px 12px;
  font-size: 14px;
  font-weight: 500;
  color: #5d5d5d;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  cursor: pointer;
  outline: none;
  transition: border-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
  flex-shrink: 0;
}

.procedure-dropdown-btn:hover,
.procedure-dropdown-btn:focus {
  border-color: #007bff;
}

.procedure-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
}

.procedure-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #5B5B5B;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  transition: background-color 0.2s;
}

.procedure-dropdown-item:hover {
  background-color: #f5f5f5;
}

.procedure-dropdown-item:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.procedure-dropdown-item:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.procedure-dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.procedure-dropdown-menu::-webkit-scrollbar-track {
  background: transparent;
}

.procedure-dropdown-menu::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.procedure-dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
.btn-filter,
.btn-export {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #fff;
  border: 1.5px solid #e5e7eb;
  border-radius: 8px;
  padding: 6px 16px;
  font-size: 15px;
  color: #222;
  cursor: pointer;
  transition: background 0.15s, border 0.15s;
  height: 36px;
  margin: 0;
}
.btn-filter:hover,
.btn-export:hover {
  background: #f7f8fa;
  border-color: #bfc5ce;
}
.statistical-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 0;
  justify-content: flex-end;
}

/* ========== PROCEDURE CARDS STYLES ========== */
/* Bố cục thẻ quy trình giống ảnh mẫu */
.procedure-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
.procedure-card-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  gap: 32px;
}
.procedure-col {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.procedure-col-left {
  min-width: 220px;
  flex: 1.2;
}
.procedure-col-center {
  min-width: 220px;
  flex: 1.2;
}
.procedure-col-right {
  min-width: 180px;
  flex: 1;
  align-items: flex-end;
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Right column vertical layout */
.procedure-col-right .procedure-priority,
.procedure-col-right .procedure-done {
  align-self: flex-end;
  display: flex;
  align-items: center;
  gap: 4px;
}

.procedure-col-right .procedure-progress-section {
  align-self: flex-end;
  text-align: right;
  min-width: 150px;
}

.procedure-progress-section .procedure-progress-label {
  text-align: right;
  margin-bottom: 8px;
  font-size: 14px;
  color: #5d5d5d;
  font-weight: 600;
}

.procedure-progress-section .procedure-progress {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.procedure-progress-section .procedure-progress-bar {
  width: 120px;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.procedure-progress-section .procedure-progress-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.procedure-progress-section .procedure-progress-text {
  font-size: 14px;
  color: #5d5d5d;
  font-weight: 600;
  min-width: 35px;
}

/* Avatar tooltip styles */
.avatar-container {
  position: relative;
  display: inline-block;
}

.avatar-tooltip {
  position: fixed;
  background: #333;
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  pointer-events: none;
  transition: all 0.2s ease-out;
}

/* Tooltip animation */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
.procedure-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 350px;
}
.procedure-row {
  display: flex;
  gap: 40px;
  font-size: 15px;
  margin-bottom: 8px;
}
.procedure-label {
  color: #5b5b5b;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 2px;
}
.procedure-value {
  font-weight: 500;
  font-size: 13px;
  color: #5b5b5b;
}
.procedure-title {
  font-weight: 500;
  font-size: 16px;
  color: #5b5b5b;
}
.procedure-id {
  color: #5b5b5b;
  font-weight: 500;
  font-size: 13px;
  margin-bottom: 0;
}
.procedure-desc {
  color: #5b5b5b;
  font-size: 13px;
  font-weight: 500;
  margin-top: 0;
}
.procedure-status {
  min-width: 180px;
  text-align: right;
}
.procedure-status-row {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  flex-direction: column;
  gap: 16px;
}
.procedure-priority, .procedure-done {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 20px; /* Đảm bảo chiều cao cố định */
}
.procedure-priority-icon, .procedure-done-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0; /* Không cho phép icon co lại */
}
.procedure-priority-text.high { font-weight: 500; font-size: 14px; color: #7c7c7c; }
.procedure-priority-text.critical { font-weight: 500; font-size: 15px; color: #7c7c7c; }
.procedure-priority-text.medium { font-weight: 500; font-size: 15px; color: #7c7c7c; }
.procedure-priority-text.low { font-weight: 500; font-size: 15px; color: #7c7c7c; }

.procedure-done-text.completed { font-weight: 500; font-size: 15px; color: #7c7c7c; }
.procedure-done-text.overdue { font-weight: 500; font-size: 15px; color: #7c7c7c; }
.procedure-done-text.pending { font-weight: 500; font-size: 15px; color: #7c7c7c; }
.procedure-done-text.in_progress { font-weight: 500; font-size: 15px; color: #7c7c7c; }
.procedure-done-text.review { font-weight: 500; font-size: 15px; color: #7c7c7c; }  
.procedure-done-text.in-progress, 
.procedure-done-text.waiting, 
.procedure-done-text.consider { color: #888; font-weight: 500; font-size: 15px; }

.procedure-progress-label {
  margin-top: 24px;
  font-size: 16px;
  color: #5b5b5b;
  text-align: left;
}
.procedure-progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}
.procedure-bar-bg {
  width: 120px;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
}
.procedure-bar-fill {
  height: 8px;
  background: #3498db;
}
.procedure-progress-value {
  font-weight: 500;
  font-size: 15px;
  color: #222;
}

/* Responsive */
@media (max-width: 1200px) {
  .statistical-filters-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-controls {
    flex-wrap: wrap;
    gap: 12px;
  }

  .procedure-dropdown-btn,
  .filter-date,
  .filter-search {
    min-width: 120px;
    flex: 1;
  }

  .date-picker-container {
    width: 320px;
    height: 320px;
  }

  .procedure-dropdown-btn {
    width: 140px;
  }

  .statistical-actions {
    align-self: flex-end;
  }
}

@media (max-width: 900px) {
  .procedure-card, .procedure-card-row {
    flex-direction: column;
    gap: 16px;
    text-align: left;
  }
  .procedure-status {
    text-align: left;
    min-width: unset;
  }
  .procedure-card-row, .procedure-col {
    flex-direction: column;
    gap: 16px;
    text-align: left;
    align-items: flex-start;
  }
  .procedure-col-right {
    align-items: flex-start;
    text-align: left;
  }

  /* Mobile layout for right column */
  .procedure-col-right .procedure-priority,
  .procedure-col-right .procedure-done {
    align-self: flex-start;
  }

  .procedure-col-right .procedure-progress-section {
    align-self: flex-start;
    text-align: left;
    min-width: unset;
  }

  .procedure-progress-section .procedure-progress-label {
    text-align: left;
  }

  .procedure-progress-section .procedure-progress {
    justify-content: flex-start;
  }

  .procedure-progress-section .procedure-progress-bar {
    width: 100px;
  }

  .filter-controls {
    flex-direction: column;
    gap: 12px;
  }

  .procedure-dropdown-btn,
  .filter-date,
  .filter-search {
    width: 100%;
    min-width: unset;
  }

  .date-picker-container {
    left: 0;
    right: 0;
    width: 100%;
    height: 300px;
    max-width: 350px;
  }

  .procedure-dropdown-btn {
    width: 100%;
  }
}

/* Loading states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: #666;
  font-size: 16px;
  margin-bottom: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Skeleton loading */
.procedure-card.skeleton {
  cursor: default;
  pointer-events: none;
  opacity: 0.8;
}

.skeleton-title {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 90%;
}

.skeleton-text {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 6px;
  width: 80%;
}

.skeleton-text:nth-child(2) {
  width: 60%;
}

.skeleton-text:nth-child(3) {
  width: 70%;
}

.skeleton-text:nth-child(4) {
  width: 85%;
}

.skeleton-progress {
  height: 8px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  width: 120px;
  margin-top: 8px;
}

/* Avatar skeleton for team members */
.skeleton-avatars {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.skeleton-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Error container */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Fade in animation for loaded data */
.procedure-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
