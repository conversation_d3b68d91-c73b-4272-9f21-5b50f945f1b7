import React, { useState, useRef, useEffect } from 'react';
import '../../styles/CreateNote.css';
import closeLoc from '../../assets/closeLoc.svg';
import loadFileIcon from "../../assets/loadfile.svg";
import fileTextIcon from "../../assets/file-text.svg";
import DocsIcon from '../../assets/docs.svg';
import PdfIcon from '../../assets/pdf.svg';
import ImageIcon from '../../assets/image.svg';
import { validateNoteForm } from '../../utils/validation';
import { showToast } from '../../utils/toastUtils';
import { PERSONAL_NOTES_ENDPOINTS } from '../../api/endpoints';

const CreateNote = ({ onClose, onCreate, onNoteCreated }) => {
  
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [errors, setErrors] = useState({ title: '', content: '' });
  const modalRef = useRef(null);

  const validate = () => {
    const validationErrors = validateNoteForm({ title, content });
    setErrors(validationErrors);
    return !validationErrors.title && !validationErrors.content;
  };

   const handleAddFile = () => {
    fileInputRef.current?.click();
  };

   const handleFileUpload = (e) => {
  const files = Array.from(e.target.files);
  if (files.length === 0) return;
  
  // Validate file size (max 20MB per file)
  const validFiles = files.filter(file => {
    if (file.size > 20 * 1024 * 1024) {
      showToast(`File ${file.name} quá lớn (tối đa 20MB)`, 'error');
      return false;
    }
    return true;
  });

  if (validFiles.length > 0) {
    console.log('File chọn:', validFiles);
    setAttachments(prev => [...prev, ...validFiles]);
    showToast(`Đã thêm ${validFiles.length} tệp`, 'success');
  }
  
  e.target.value = "";
};

  const [formData, setFormData] = useState({
  name: "",
  description: "",
  startDate: "",
  endDate: "",
  priority: "medium",
  members: [],
});
const [attachments, setAttachments] = useState([]);

  const removeFile = (index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Hàm để chọn icon dựa trên loại file
  const getFileIcon = (fileName) => {
    const ext = (fileName || '').split('.').pop().toLowerCase();
    if (ext === 'pdf') return PdfIcon;
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) return ImageIcon;
    if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'].includes(ext)) return DocsIcon;
    return DocsIcon; // Mặc định cho các loại khác
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    if (!title.trim()) {
      showToast('Vui lòng nhập tiêu đề ghi chú!', 'error');
      return;
    }
    let createdNoteId = null;
    let createdNoteRaw = null;
    if (onCreate) {
      const result = await onCreate({ title, content });
      if (result) {
        if (result.success === false) {
          showToast(result.message || 'Tạo ghi chú thất bại!', 'error');
          return;
        }
        if (result.id) createdNoteId = result.id;
        else if (result._id) createdNoteId = result._id;
        else if (result.data && result.data._id) createdNoteId = result.data._id;
        createdNoteRaw = result;
      }
    }
    if (!createdNoteId) {
      showToast('Không thể lấy ID ghi chú!', 'error');
      return;
    }
    let updatedNote = createdNoteRaw;
    if (attachments.length > 0 && createdNoteId) {
      const form = new FormData();
      attachments.forEach(file => form.append('files', file));
      for (let pair of form.entries()) {
        console.log('FormData:', pair[0], pair[1]);
      }
      const token = localStorage.getItem('token');
      try {
        const res = await fetch(
          PERSONAL_NOTES_ENDPOINTS.UPLOAD_FILES(createdNoteId),
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
            },
            body: form,
            signal: AbortSignal.timeout(60000),
          }
        );
        let uploadResult = null;
        try {
          uploadResult = await res.json();
          console.log('Kết quả upload file:', uploadResult);
        } catch (parseError) {
          console.error('Lỗi parse response:', parseError);
        }
        if (res.ok && uploadResult && uploadResult.success) {
          showToast('Tải tệp thành công!', 'success');
          try {
            const detailRes = await fetch(PERSONAL_NOTES_ENDPOINTS.UPDATE_NOTE(createdNoteId), {
              headers: { 'Authorization': `Bearer ${token}` }
            });
            if (detailRes.ok) {
              const detailData = await detailRes.json();
              updatedNote = detailData;
              if (onNoteCreated) {
                onNoteCreated(updatedNote);
              }
            }
          } catch (detailError) {
            console.error('Lỗi fetch chi tiết ghi chú:', detailError);
          }
        } else {
          const errorMessage = uploadResult?.message || 'Tải tệp thất bại!';
          showToast(errorMessage, 'error');
          console.error('Upload failed:', res.status, res.statusText, uploadResult);
        }
      } catch (uploadError) {
        console.error('Lỗi upload file:', uploadError);
        if (uploadError.name === 'TimeoutError') {
          showToast('Upload timeout - file quá lớn hoặc kết nối chậm!', 'error');
        } else if (uploadError.name === 'AbortError') {
          showToast('Upload bị hủy!', 'error');
        } else {
          showToast('Có lỗi khi tải tệp!', 'error');
        }
      }
    } else {
      if (onNoteCreated) {
        onNoteCreated(updatedNote);
      }
    }
    setTitle('');
    setContent('');
    setAttachments([]);
    if (fileInputRef.current) fileInputRef.current.value = "";
    showToast('Tạo ghi chú thành công!', 'success');
    onClose && onClose();
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose && onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

    const fileInputRef = useRef(null);

  return (
    <div className="create-note-modal">
      <form onSubmit={handleSubmit} className="create-note-form" ref={modalRef}>
        <div className="create-note-header">
          <h2 className="create-note-title">Tạo ghi chú mới</h2>
          <button type="button" onClick={onClose} className="create-note-close">
            <img src={closeLoc} alt="Close" />
          </button>
        </div>
        <div className="create-note-input-container">
          <input
            type="text"
            placeholder="Tiêu đề"
            value={title}
            onChange={e => setTitle(e.target.value)}
            className={`create-note-input ${errors.title ? 'create-note-input-error' : ''}`}
          />
          {errors.title && <div className="create-note-error-message">{errors.title}</div>}
        </div>
        <div className="create-note-textarea-container">
          <textarea
            placeholder="Nội dung ghi chú"
            value={content}
            onChange={e => setContent(e.target.value)}
            rows={5}
            className={`create-note-textarea ${errors.content ? 'create-note-input-error' : ''}`}
          />
          {errors.content && <div className="create-note-error-message">{errors.content}</div>}
        </div>
        <div className="job-panel-rows">
            <div className="job-panel-label">Tệp đính kèm</div>
            <div className="job-panel-value" style={{flexDirection: 'column', alignItems: 'flex-start', gap: 0}}>
              <div className="job-panel-file-upload-custom" onClick={handleAddFile}>
                <input
                  type="file"
                  multiple
                  accept="*"
                  onChange={handleFileUpload}
                  style={{ display: "none" }}
                  ref={fileInputRef}
                />
                <img src={loadFileIcon} alt="Tải tệp" className="job-panel-file-upload-icon" />
                <span className="job-panel-file-upload-text">Bấm vào để tải lên tệp</span>
              </div>
              {attachments.length > 0 && (
                <div className="job-panel-file-list" style={{width: '100%'}}>
                  {attachments.map((file, idx) => (
                    <div className="job-panel-file-item" key={idx}>
                      <img src={getFileIcon(file.name)} alt="file" className="job-panel-file-icon" />
                      <div className="job-panel-file-info">
                        <span className="job-panel-file-name">{file.name}</span>
                        <span className="job-panel-file-size">{formatFileSize(file.size)}</span>
                      </div>
                      <button type="button" className="job-panel-remove-file-btn" onClick={() => removeFile(idx)} title="Xóa tệp">×</button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        <div className="create-note-buttons">
          <button type="button" className="create-note-cancel" onClick={onClose}>
            Hủy
          </button>
          <button type="submit" className="create-note-submit">
            Tạo
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateNote;
