import React, { useState, useEffect } from 'react';
import { ADMIN_ENDPOINTS } from '../../api/endpoints';
import '../../styles/UserSessionManagement.css';

const UserSessionManagement = ({ userId, onClose }) => {
  const [sessionInfo, setSessionInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newIp, setNewIp] = useState('');
  const [reason, setReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  const getToken = () => localStorage.getItem('token');

  useEffect(() => {
    fetchSessionInfo();
  }, [userId]);

  const fetchSessionInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch(ADMIN_ENDPOINTS.GET_USER_SESSION_INFO(userId), {
        headers: {
          'Authorization': `<PERSON><PERSON> ${getToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Không thể lấy thông tin session');
      }

      const data = await response.json();
      setSessionInfo(data.data);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChangeAllowedIp = async () => {
    if (!newIp.trim()) {
      alert('Vui lòng nhập IP mới');
      return;
    }

    try {
      setActionLoading(true);
      const response = await fetch(ADMIN_ENDPOINTS.CHANGE_ALLOWED_IP(userId), {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          newIp: newIp.trim(),
          reason: reason.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Không thể thay đổi IP');
      }

      const data = await response.json();
      alert(data.message);
      setNewIp('');
      setReason('');
      fetchSessionInfo();
    } catch (error) {
      alert('Lỗi: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleInvalidateSession = async () => {
    if (!confirm('Bạn có chắc chắn muốn vô hiệu hóa session của user này?')) {
      return;
    }

    try {
      setActionLoading(true);
      const response = await fetch(ADMIN_ENDPOINTS.INVALIDATE_USER_SESSION(userId), {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: reason.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Không thể vô hiệu hóa session');
      }

      const data = await response.json();
      alert(data.message);
      setReason('');
      fetchSessionInfo();
    } catch (error) {
      alert('Lỗi: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleToggleIpRestriction = async (enabled) => {
    try {
      setActionLoading(true);
      const response = await fetch(ADMIN_ENDPOINTS.TOGGLE_IP_RESTRICTION(userId), {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          enabled,
          reason: reason.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Không thể thay đổi trạng thái IP restriction');
      }

      const data = await response.json();
      alert(data.message);
      setReason('');
      fetchSessionInfo();
    } catch (error) {
      alert('Lỗi: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Không có';
    return new Date(dateString).toLocaleString('vi-VN');
  };

  if (loading) {
    return (
      <div className="session-management-modal">
        <div className="session-management-content">
          <div className="loading">Đang tải...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="session-management-modal">
        <div className="session-management-content">
          <div className="error">Lỗi: {error}</div>
          <button onClick={onClose}>Đóng</button>
        </div>
      </div>
    );
  }

  return (
    <div className="session-management-modal">
      <div className="session-management-content">
        <div className="modal-header">
          <h2>Quản lý Session & IP - {sessionInfo?.fullName}</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>

        <div className="session-info">
          <h3>Thông tin Session hiện tại</h3>
          <div className="info-grid">
            <div className="info-item">
              <label>Session ID:</label>
              <span>{sessionInfo?.activeSessionId || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>Thời gian tạo:</label>
              <span>{formatDate(sessionInfo?.sessionCreatedAt)}</span>
            </div>
            <div className="info-item">
              <label>IP hiện tại:</label>
              <span>{sessionInfo?.sessionDeviceInfo?.ip || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>Browser:</label>
              <span>{sessionInfo?.sessionDeviceInfo?.browser || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>OS:</label>
              <span>{sessionInfo?.sessionDeviceInfo?.os || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>Tổng số session đang hoạt động:</label>
              <span>{sessionInfo?.totalActiveSessions || 0}</span>
            </div>
          </div>
        </div>

        {sessionInfo?.activeSessions && sessionInfo.activeSessions.length > 0 && (
          <div className="active-sessions">
            <h3>Danh sách Session đang hoạt động</h3>
            <div className="sessions-list">
              {sessionInfo.activeSessions.filter(s => s.isActive).map((session, index) => (
                <div key={index} className="session-item">
                  <div className="session-info">
                    <span className="session-id">Session ID: {session.sessionId}</span>
                    <span className="browser">{session.deviceInfo?.browser} trên {session.deviceInfo?.os}</span>
                    <span className="ip">IP: {session.deviceInfo?.ip}</span>
                    <span className="time">{formatDate(session.createdAt)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="ip-restriction-info">
          <h3>Thông tin IP Restriction</h3>
          <div className="info-grid">
            <div className="info-item">
              <label>IP được phép:</label>
              <span>{sessionInfo?.allowedIp || 'Chưa thiết lập'}</span>
            </div>
            <div className="info-item">
              <label>Trạng thái IP Restriction:</label>
              <span className={sessionInfo?.ipRestrictionEnabled ? 'enabled' : 'disabled'}>
                {sessionInfo?.ipRestrictionEnabled ? 'Bật' : 'Tắt'}
              </span>
            </div>
          </div>
        </div>

        <div className="action-section">
          <h3>Thao tác</h3>
          
          <div className="action-group">
            <h4>Thay đổi IP được phép</h4>
            <div className="input-group">
              <input
                type="text"
                placeholder="Nhập IP mới"
                value={newIp}
                onChange={(e) => setNewIp(e.target.value)}
              />
              <input
                type="text"
                placeholder="Lý do (tùy chọn)"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />
              <button 
                onClick={handleChangeAllowedIp}
                disabled={actionLoading || !newIp.trim()}
              >
                {actionLoading ? 'Đang xử lý...' : 'Thay đổi IP'}
              </button>
            </div>
          </div>

          <div className="action-group">
            <h4>Quản lý Session</h4>
            <div className="button-group">
              <button 
                onClick={handleInvalidateSession}
                disabled={actionLoading}
                className="danger"
              >
                {actionLoading ? 'Đang xử lý...' : 'Vô hiệu hóa Session'}
              </button>
            </div>
          </div>

          <div className="action-group">
            <h4>Quản lý IP Restriction</h4>
            <div className="button-group">
              <button 
                onClick={() => handleToggleIpRestriction(!sessionInfo?.ipRestrictionEnabled)}
                disabled={actionLoading}
                className={sessionInfo?.ipRestrictionEnabled ? 'warning' : 'success'}
              >
                {actionLoading ? 'Đang xử lý...' : 
                  sessionInfo?.ipRestrictionEnabled ? 'Tắt IP Restriction' : 'Bật IP Restriction'}
              </button>
            </div>
          </div>
        </div>

        {sessionInfo?.ipLoginHistory && sessionInfo.ipLoginHistory.length > 0 && (
          <div className="login-history">
            <h3>Lịch sử đăng nhập</h3>
            <div className="history-list">
              {sessionInfo.ipLoginHistory.slice(-10).reverse().map((login, index) => (
                <div key={index} className="history-item">
                  <div className="history-info">
                    <span className="ip">{login.ip}</span>
                    <span className="browser">{login.browser} trên {login.os}</span>
                    <span className="time">{formatDate(login.loginTime)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="modal-footer">
          <button onClick={onClose}>Đóng</button>
        </div>
      </div>
    </div>
  );
};

export default UserSessionManagement; 